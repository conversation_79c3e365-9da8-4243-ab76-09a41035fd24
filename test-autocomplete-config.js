// Simple test to verify autocomplete configuration is working
// This file can be used to test if the autocomplete status bar appears and configuration works

console.log("Testing autocomplete configuration...");

// Test function to simulate some code that might trigger autocomplete
function testFunction() {
    const message = "Hello, world!";
    console.log(message);
    
    // Add some more lines to test autocomplete context
    const numbers = [1, 2, 3, 4, 5];
    const doubled = numbers.map(n => n * 2);
    
    return doubled;
}

// Call the test function
testFunction();
